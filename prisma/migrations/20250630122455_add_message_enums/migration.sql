/*
  Warnings:

  - The `message_type` column on the `chat_messages` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `is_active` on the `chat_users` table. All the data in the column will be lost.
  - You are about to drop the column `is_admin` on the `chat_users` table. All the data in the column will be lost.
  - You are about to drop the column `last_read_at` on the `chat_users` table. All the data in the column will be lost.
  - You are about to drop the column `is_active` on the `chats` table. All the data in the column will be lost.
  - You are about to drop the column `last_message_at` on the `chats` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "message_types" AS ENUM ('text', 'image', 'file', 'sticker');

-- CreateEnum
CREATE TYPE "message_status" AS ENUM ('sending', 'delivered', 'read', 'failed');

-- AlterTable
ALTER TABLE "chat_messages" ADD COLUMN     "message_status" "message_status" NOT NULL DEFAULT 'sending',
DROP COLUMN "message_type",
ADD COLUMN     "message_type" "message_types" NOT NULL DEFAULT 'text';

-- AlterTable
ALTER TABLE "chat_users" DROP COLUMN "is_active",
DROP COLUMN "is_admin",
DROP COLUMN "last_read_at";

-- AlterTable
ALTER TABLE "chats" DROP COLUMN "is_active",
DROP COLUMN "last_message_at";
