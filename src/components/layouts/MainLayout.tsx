'use client';

import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';
import Sidebar from './Sidebar';
import Header from './Header';
import { SocketProvider, useSocket } from '@/lib/socket-context';
import { useSocketEvent, useSocketEmit } from '@/hooks/useSocket';

const LayoutContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh; /* Fixed height instead of min-height */
  width: 100%;
  overflow: hidden; /* Prevent container from overflowing */
`;

const ContentSection = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`;

const MainSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const ContentWrapper = styled.main`
  flex: 1;
  overflow-y: auto; /* Allow content to scroll vertically */
  overflow-x: hidden; /* Prevent horizontal scrolling */
  // padding: 1rem;
  background-color: var(--background);
  padding-top: 80px;
`;

export default function MainLayout({ children }: { children: React.ReactNode }) {
  const headerRef = useRef<HTMLElement>(null);

  return (
    <SocketProvider>
      <LayoutContainer>
        <Header ref={headerRef} />
        <ContentSection>
          <Sidebar />
          <MainSection>
            <ContentWrapper>{children}</ContentWrapper>
          </MainSection>
        </ContentSection>
      </LayoutContainer>
    </SocketProvider>
  );
}
