import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';

// Initialize S3 client
const s3Client = new S3Client({
    region: process.env.AWS_REGION || 'us-east-1',
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
    },
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME!;

// File type configurations
export const FILE_TYPE_CONFIGS = {
    images: {
        maxSize: 10 * 1024 * 1024, // 10MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    },
    documents: {
        maxSize: 50 * 1024 * 1024, // 50MB
        allowedTypes: [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
        ],
        extensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt'],
    },
    avatars: {
        maxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
        extensions: ['.jpg', '.jpeg', '.png', '.webp'],
    },
    chat: {
        maxSize: 25 * 1024 * 1024, // 25MB
        allowedTypes: [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf',
            'text/plain',
        ],
        extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.pdf', '.txt'],
    },
} as const;


export type FileType = keyof typeof FILE_TYPE_CONFIGS;

// Interface for file validation (compatible with both browser File and Node.js)
export interface FileForValidation {
    size: number;
    type: string;
    name: string;
}

// Generate S3 key with user isolation
export function generateS3Key(userId: string, fileType: FileType, fileName: string): string {
    const timestamp = Date.now();
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    return `hia-suag-ma/user-${userId}/${fileType}/${timestamp}-${sanitizedFileName}`;
}

// Validate file type and size
export function validateFile(file: FileForValidation, fileType: FileType): { valid: boolean; error?: string } {
    const config = FILE_TYPE_CONFIGS[fileType];

    // Check file size
    if (file.size > config.maxSize) {
        return {
            valid: false,
            error: `File size exceeds ${config.maxSize / (1024 * 1024)}MB limit`,
        };
    }

    // Check file type
    if (!(config.allowedTypes as readonly string[]).includes(file.type)) {
        return {
            valid: false,
            error: `File type ${file.type} is not allowed for ${fileType}`,
        };
    }

    // Check file extension
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!(config.extensions as readonly string[]).includes(fileExtension)) {
        return {
            valid: false,
            error: `File extension ${fileExtension} is not allowed for ${fileType}`,
        };
    }

    return { valid: true };
}

// Upload file to S3
export async function uploadToS3(
    key: string,
    file: Buffer,
    contentType: string
): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
        const command = new PutObjectCommand({
            Bucket: BUCKET_NAME,
            Key: key,
            Body: file,
            ContentType: contentType,
        });

        await s3Client.send(command);

        const url = `https://${BUCKET_NAME}.s3.${process.env.AWS_REGION || 'us-east-1'}.amazonaws.com/${key}`;

        return { success: true, url };
    } catch (error) {
        console.error('S3 upload error:', error);
        return { success: false, error: 'Failed to upload file to S3' };
    }
}



// Delete file from S3
export async function deleteFromS3(key: string): Promise<{ success: boolean; error?: string }> {
    try {
        const command = new DeleteObjectCommand({
            Bucket: BUCKET_NAME,
            Key: key,
        });

        await s3Client.send(command);

        return { success: true };
    } catch (error) {
        console.error('S3 delete error:', error);
        return { success: false, error: 'Failed to delete file from S3' };
    }
}

// Get file from S3
export async function getFromS3(key: string): Promise<{ success: boolean; data?: Buffer; error?: string }> {
    try {
        const command = new GetObjectCommand({
            Bucket: BUCKET_NAME,
            Key: key,
        });

        const response = await s3Client.send(command);
        const data = await response.Body?.transformToByteArray();

        if (!data) {
            return { success: false, error: 'No data received from S3' };
        }

        return { success: true, data: Buffer.from(data) };
    } catch (error) {
        console.error('S3 get error:', error);
        return { success: false, error: 'Failed to get file from S3' };
    }
}

// Check if user owns the file (based on S3 key)
export function isUserFile(userId: string, s3Key: string): boolean {
    return s3Key.startsWith(`user-${userId}/`);
}
