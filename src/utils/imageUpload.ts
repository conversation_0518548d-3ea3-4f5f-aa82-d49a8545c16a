/**
 * Upload image to server and return the URL
 */
export async function uploadImageToServer(file: File, token: string): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('fileType', 'images');

  const response = await fetch('/api/v1/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Upload failed (${response.status}): ${errorText}`);
  }

  const result = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || 'Upload failed');
  }
  
  if (!result.data?.url) {
    throw new Error('No URL returned from upload');
  }
  
  return result.data.url;
}

/**
 * Extract base64 images from HTML content and upload them to server
 * Replace base64 data URLs with server URLs
 */
export async function processImagesInContent(
  htmlContent: string, 
  token: string
): Promise<string> {
  // Find all img tags with base64 data URLs
  const imgRegex = /<img[^>]+src="data:image\/[^;]+;base64,[^"]*"[^>]*>/g;
  const matches = htmlContent.match(imgRegex);
  
  if (!matches) {
    return htmlContent;
  }

  let processedContent = htmlContent;

  // Process each image
  for (const imgTag of matches) {
    try {
      // Extract the base64 data URL
      const srcMatch = imgTag.match(/src="(data:image\/[^;]+;base64,[^"]*)"/);
      if (!srcMatch) continue;

      const dataUrl = srcMatch[1];
      
      // Convert base64 to File
      const file = await dataUrlToFile(dataUrl);
      
      // Upload to server
      const serverUrl = await uploadImageToServer(file, token);
      
      // Replace the base64 URL with server URL
      const newImgTag = imgTag.replace(dataUrl, serverUrl);
      processedContent = processedContent.replace(imgTag, newImgTag);
      
    } catch (error) {
      console.error('Failed to process image:', error);
      // Keep the original base64 image if upload fails
    }
  }

  return processedContent;
}

/**
 * Convert data URL to File object
 */
async function dataUrlToFile(dataUrl: string): Promise<File> {
  const response = await fetch(dataUrl);
  const blob = await response.blob();
  
  // Extract mime type and create filename
  const mimeType = dataUrl.split(';')[0].split(':')[1];
  const extension = mimeType.split('/')[1];
  const filename = `image_${Date.now()}.${extension}`;
  
  return new File([blob], filename, { type: mimeType });
}

/**
 * Create upload function for TextEditor component
 */
export function createImageUploadFunction(token: string) {
  return async (file: File): Promise<string> => {
    return await uploadImageToServer(file, token);
  };
}
