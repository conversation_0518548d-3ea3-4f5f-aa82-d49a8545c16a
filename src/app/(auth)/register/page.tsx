'use client';

import RegisterForm from './components/RegisterForm';
import styled from 'styled-components';
import Squares from '../login/Squares';
import RotatingText from '@/components/ui/RotationText';

const RegisterPageContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background: transparent;
  position: relative;
  min-width: 100vw;
  height: 100vh;
  align-items: top;
  justify-content: center;
`;

const FormSection = styled.div`
  flex: 1;
  display: flex;
  align-items: start;
  justify-content: center;
  position: absolute;
  z-index: 2;
  max-height: 100vh;
  overflow-y: auto;
  padding-top: 4rem;
  padding-bottom: 2rem;
`;

const FormCard = styled.div`
  width: 100%;
  max-width: 450px;
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #4f46e5;
  position: relative;
  z-index: 10;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  font-size: 2.25rem;
  font-weight: 700;
  color: #474747;
`;

const LogoCircle = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
`;

export default function RegisterPage() {
  return (
    <RegisterPageContainer>
      <Squares
        speed={0.1}
        squareSize={40}
        direction="diagonal"
        borderColor="#dedede66"
        hoverFillColor="#4F46E566"
      />
      <FormSection>
        <FormCard>
          <Logo>
            <span style={{ color: '#474747' }}>เฮีย</span> ·{' '}
            <span style={{ color: '#f56042' }}>สั่ง</span> ·{' '}
            <span style={{ color: '#474747' }}>มา</span>
            <RotatingText
              texts={['Task', 'Management', 'Reward', 'Productivity']}
              mainClassName="flex items-center text-gray-500 text-lg font-bold px-4 h-10 rounded-md bg-[#474747] text-white"
              staggerFrom={'last'}
              staggerDuration={0.025}
              splitLevelClassName="overflow-hidden"
              transition={{ type: 'spring', damping: 30, stiffness: 400 }}
              rotationInterval={2000}
            />
          </Logo>
          <RegisterForm />
        </FormCard>
      </FormSection>
    </RegisterPageContainer>
  );
}
