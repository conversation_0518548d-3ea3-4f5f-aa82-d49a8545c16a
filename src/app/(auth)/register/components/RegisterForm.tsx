'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { useForm } from 'react-hook-form';
import { Mail, Lock, User, Building, Phone } from 'lucide-react';
import Link from 'next/link';

// Types
interface RegisterFormProps {
  onSubmit?: (data: RegisterFormData) => void;
  isLoading?: boolean;
  error?: string | null;
}

interface RegisterFormData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone: string;
  organizationName: string;
}

// Styled Components
const FormContainer = styled.div`
  width: 100%;
`;

const FormHeader = styled.div`
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.75rem;
`;

const Subtitle = styled.p`
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const FormRow = styled.div`
  display: flex;
  gap: 1rem;

  > * {
    flex: 1;
  }

  @media (max-width: 640px) {
    flex-direction: column;
    gap: 1.5rem;
  }
`;

const Label = styled.label`
  font-size: 0.9rem;
  color: #4b5563;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const InputWrapper = styled.div`
  position: relative;
`;

const IconWrapper = styled.div`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  color: #111827;
  background-color: #f9fafb;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    background-color: white;
  }

  &::placeholder {
    color: #9ca3af;
  }
`;

const ErrorMessage = styled.div`
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const LoginLink = styled.div`
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.875rem;
  color: #4b5563;

  a {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
`;

export default function RegisterForm({
  onSubmit,
  isLoading: propIsLoading,
  error: propError,
}: RegisterFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(propIsLoading || false);
  const [error, setError] = useState<string | null>(propError || null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<RegisterFormData>();

  const password = watch('password');

  const handleRegister = async (data: RegisterFormData) => {
    if (onSubmit) {
      onSubmit(data);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Check if passwords match
      if (data.password !== data.confirmPassword) {
        setError('Passwords do not match');
        setIsLoading(false);
        return;
      }

      const response = await fetch('/api/v1/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          firstName: data.firstName,
          lastName: data.lastName,
          phone: data.phone || '',
          organizationName: data.organizationName,
        }),
      });

      // Check if the response is JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error(
          'API endpoint not found or not returning JSON. Please check the API configuration.'
        );
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Registration failed. Please try again.');
      }

      const result = await response.json().catch(() => ({}));

      // Registration successful, redirect to login page
      router.push('/login?registered=true');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      console.error('Registration error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <FormContainer>
      <FormHeader>
        <Title>Create your account</Title>
        <Subtitle>Fill in your details to join our learning platform</Subtitle>
      </FormHeader>
      <Form onSubmit={handleSubmit(handleRegister)}>
        <FormRow>
          <FormGroup>
            <Label htmlFor="firstName">First Name</Label>
            <InputWrapper>
              <IconWrapper>
                <User size={18} />
              </IconWrapper>
              <Input
                id="firstName"
                type="text"
                placeholder="John"
                {...register('firstName', {
                  required: 'First name is required',
                })}
              />
            </InputWrapper>
            {errors.firstName && <ErrorMessage>{errors.firstName.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="lastName">Last Name</Label>
            <InputWrapper>
              <IconWrapper>
                <User size={18} />
              </IconWrapper>
              <Input
                id="lastName"
                type="text"
                placeholder="Doe"
                {...register('lastName', {
                  required: 'Last name is required',
                })}
              />
            </InputWrapper>
            {errors.lastName && <ErrorMessage>{errors.lastName.message}</ErrorMessage>}
          </FormGroup>
        </FormRow>

        <FormGroup>
          <Label htmlFor="email">Email</Label>
          <InputWrapper>
            <IconWrapper>
              <Mail size={18} />
            </IconWrapper>
            <Input
              id="email"
              type="text"
              placeholder="<EMAIL>"
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: 'Please enter a valid email address',
                },
              })}
            />
          </InputWrapper>
          {errors.email && <ErrorMessage>{errors.email.message}</ErrorMessage>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="phone">Phone Number (Optional)</Label>
          <InputWrapper>
            <IconWrapper>
              <Phone size={18} />
            </IconWrapper>
            <Input id="phone" type="text" placeholder="+****************" {...register('phone')} />
          </InputWrapper>
          {errors.phone && <ErrorMessage>{errors.phone.message}</ErrorMessage>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="organizationName">Organization Name</Label>
          <InputWrapper>
            <IconWrapper>
              <Building size={18} />
            </IconWrapper>
            <Input
              id="organizationName"
              type="text"
              placeholder="Your Organization"
              {...register('organizationName', {
                required: 'Organization name is required',
              })}
            />
          </InputWrapper>
          {errors.organizationName && (
            <ErrorMessage>{errors.organizationName.message}</ErrorMessage>
          )}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="password">Password</Label>
          <InputWrapper>
            <IconWrapper>
              <Lock size={18} />
            </IconWrapper>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              {...register('password', {
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },
                pattern: {
                  value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                  message:
                    'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
                },
              })}
            />
          </InputWrapper>
          {errors.password && <ErrorMessage>{errors.password.message}</ErrorMessage>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <InputWrapper>
            <IconWrapper>
              <Lock size={18} />
            </IconWrapper>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="••••••••"
              {...register('confirmPassword', {
                required: 'Please confirm your password',
                validate: value => value === password || 'Passwords do not match',
              })}
            />
          </InputWrapper>
          {errors.confirmPassword && <ErrorMessage>{errors.confirmPassword.message}</ErrorMessage>}
        </FormGroup>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <Button
          type="submit"
          $fullWidth
          disabled={isLoading}
          style={{
            backgroundColor: '#4f46e5',
            borderRadius: '8px',
            padding: '0.75rem',
            fontSize: '1rem',
            fontWeight: '500',
          }}
        >
          {isLoading ? 'Creating Account...' : 'Create Account'}
        </Button>

        <LoginLink>
          Already have an account? <Link href="/login">Sign in</Link>
        </LoginLink>
      </Form>
    </FormContainer>
  );
}
