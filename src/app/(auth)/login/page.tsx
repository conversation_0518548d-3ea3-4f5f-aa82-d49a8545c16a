'use client';

import LoginForm from './components/LoginForm';
import styled from 'styled-components';
import Squares from './Squares';
import RotatingText from '@/components/ui/RotationText';

const LoginPageContainer = styled.div`
  display: flex;
  min-height: 100vh;
  overflow: hidden;
  background: transparent;
  position: relative;
  min-width: 100vw;
  align-items: center;
  justify-content: center;
`;

const FormSection = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: absolute;
  z-index: 2;
`;

const FormCard = styled.div`
  width: 100%;
  max-width: 450px;
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  position: relative;
  z-index: 10;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  font-size: 2.25rem;
  font-weight: 700;
  color: #474747;
`;

const LogoCircle = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
`;

export default function LoginPage() {
  return (
    <LoginPageContainer>
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          minWidth: '100vw',
          minHeight: '100vh',
          zIndex: 0,
        }}
      >
        <Squares
          speed={0.1}
          squareSize={40}
          direction="diagonal"
          borderColor="#dedede66"
          hoverFillColor="#4F46E566"
        />
      </div>
      <FormSection>
        <FormCard>
          <Logo>
            <span style={{ color: '#474747' }}>เฮีย</span> ·{' '}
            <span style={{ color: '#f56042' }}>สั่ง</span> ·{' '}
            <span style={{ color: '#474747' }}>มา</span>
            <RotatingText
              texts={['Task', 'Management', 'Reward', 'Productivity']}
              mainClassName="flex items-center text-gray-500 text-lg font-bold px-4 h-10 rounded-md bg-[#474747] text-white"
              staggerFrom={'last'}
              staggerDuration={0.025}
              splitLevelClassName="overflow-hidden"
              transition={{ type: 'spring', damping: 30, stiffness: 400 }}
              rotationInterval={2000}
            />
          </Logo>
          <LoginForm />
        </FormCard>
      </FormSection>
    </LoginPageContainer>
  );
}
