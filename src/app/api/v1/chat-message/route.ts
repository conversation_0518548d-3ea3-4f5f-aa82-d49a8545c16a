import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import { MessageType, MessageStatus } from '@/generated/prisma';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

/**
 * GET API for chat messages
 *
 * This API supports multiple modes:
 *
 * 1. Get a single message by ID:
 *    - Path: /api/v1/chat-message?id=123
 *    - Returns: Detailed message information
 *
 * 2. Get messages for a specific chat:
 *    - Path: /api/v1/chat-message?chatId=123
 *    - Optional query parameters:
 *      - page: Page number (default: 1)
 *      - limit: Messages per page (default: 50, max: 100)
 *      - before: Get messages before this message ID
 *      - after: Get messages after this message ID
 *    - Returns: List of messages in the chat
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const messageId = url.searchParams.get('id');
    const chatId = url.searchParams.get('chatId');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 100);
    const before = url.searchParams.get('before');
    const after = url.searchParams.get('after');

    // Get a specific message by ID
    if (messageId) {
      const messageIdNum = Number(messageId);
      if (isNaN(messageIdNum)) {
        return NextResponse.json({ error: 'Invalid message ID' }, { status: 400 });
      }

      const message = await prisma.chatMessage.findUnique({
        where: { id: messageIdNum },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              imageUrl: true,
            },
          },
          chat: {
            select: {
              id: true,
              name: true,
              chatType: true,
            },
          },
        },
      });

      if (!message) {
        return NextResponse.json({ error: 'Message not found' }, { status: 404 });
      }

      // Check if user has access to this chat
      const hasAccess = await prisma.chatUser.findFirst({
        where: {
          chatId: message.chatId,
          userId: auth.userId,
        },
      });

      if (!hasAccess && !auth.isAdmin && !auth.isOwner) {
        return NextResponse.json(
          { error: 'You do not have access to this message' },
          { status: 403 }
        );
      }

      return NextResponse.json({ message });
    }

    // Get messages for a specific chat
    if (chatId) {
      const chatIdNum = Number(chatId);
      if (isNaN(chatIdNum)) {
        return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
      }

      // Check if user has access to the chat
      const hasAccess = await prisma.chatUser.findFirst({
        where: {
          chatId: chatIdNum,
          userId: auth.userId,
        },
      });

      if (!hasAccess && !auth.isAdmin && !auth.isOwner) {
        return NextResponse.json({ error: 'You do not have access to this chat' }, { status: 403 });
      }

      // Build where clause for pagination
      const where: any = { chatId: chatIdNum };

      if (before) {
        const beforeNum = Number(before);
        if (!isNaN(beforeNum)) {
          where.id = { lt: beforeNum };
        }
      }

      if (after) {
        const afterNum = Number(after);
        if (!isNaN(afterNum)) {
          where.id = { gt: afterNum };
        }
      }

      const messages = await prisma.chatMessage.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              imageUrl: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: (page - 1) * limit,
      });

      // Get total count for pagination
      const totalCount = await prisma.chatMessage.count({
        where: { chatId: chatIdNum },
      });

      return NextResponse.json({
        messages,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasMore: page * limit < totalCount,
        },
      });
    }

    return NextResponse.json({ error: 'Please specify id or chatId parameter' }, { status: 400 });
  } catch (error) {
    console.error('Error fetching chat messages:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST API to send a message to a chat
 *
 * Request body:
 * {
 *   "chatId": 123 (required),
 *   "content": "Message content" (required),
 *   "messageType": "TEXT" | "IMAGE" | "FILE" | "STICKER" (optional, default: "TEXT")
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { chatId, content, messageType = 'TEXT' } = body;

    // Validate required fields
    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
    }

    if (!content || content.trim() === '') {
      return NextResponse.json({ error: 'Message content is required' }, { status: 400 });
    }

    const chatIdNum = Number(chatId);
    if (isNaN(chatIdNum)) {
      return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
    }

    // Validate message type
    const validMessageTypes = ['TEXT', 'IMAGE', 'FILE', 'STICKER'];
    if (!validMessageTypes.includes(messageType.toUpperCase())) {
      return NextResponse.json(
        { error: 'Invalid message type. Must be one of: TEXT, IMAGE, FILE, STICKER' },
        { status: 400 }
      );
    }

    // Check if user is participant in the chat
    const chatUser = await prisma.chatUser.findFirst({
      where: {
        chatId: chatIdNum,
        userId: auth.userId,
      },
      include: {
        chat: true,
      },
    });

    if (!chatUser) {
      return NextResponse.json(
        { error: 'You are not a participant in this chat' },
        { status: 403 }
      );
    }

    // Check if chat is active
    if (!chatUser.chat.isActive) {
      return NextResponse.json({ error: 'Cannot send messages to inactive chat' }, { status: 403 });
    }

    // Create the message
    const message = await prisma.chatMessage.create({
      data: {
        chatId: chatIdNum,
        userId: auth.userId,
        content: content.trim(),
        messageType: messageType.toUpperCase() as MessageType,
        messageStatus: 'DELIVERED' as MessageStatus,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            imageUrl: true,
          },
        },
        chat: {
          select: {
            id: true,
            name: true,
            chatType: true,
          },
        },
      },
    });

    // Update chat's last message timestamp
    await prisma.chat.update({
      where: { id: chatIdNum },
      data: { lastMessageAt: new Date() },
    });

    return NextResponse.json({ message }, { status: 201 });
  } catch (error) {
    console.error('Error sending message:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH API to update a message
 *
 * Request body:
 * {
 *   "id": 123 (required),
 *   "messageStatus": "DELIVERED" | "READ" | "FAILED" (optional)
 * }
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id, messageStatus } = body;

    if (!id) {
      return NextResponse.json({ error: 'Message ID is required' }, { status: 400 });
    }

    const messageIdNum = Number(id);
    if (isNaN(messageIdNum)) {
      return NextResponse.json({ error: 'Invalid message ID' }, { status: 400 });
    }

    // Check if message exists
    const existingMessage = await prisma.chatMessage.findUnique({
      where: { id: messageIdNum },
      include: {
        chat: {
          include: {
            chatUsers: {
              where: { userId: auth.userId },
            },
          },
        },
      },
    });

    if (!existingMessage) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    }

    // Check if user has access to this chat
    const hasAccess = existingMessage.chat.chatUsers.length > 0;
    if (!hasAccess && !auth.isAdmin && !auth.isOwner) {
      return NextResponse.json(
        { error: 'You do not have access to this message' },
        { status: 403 }
      );
    }

    // Validate message status if provided
    if (messageStatus) {
      const validStatuses = ['SENDING', 'DELIVERED', 'READ', 'FAILED'];
      if (!validStatuses.includes(messageStatus.toUpperCase())) {
        return NextResponse.json(
          { error: 'Invalid message status. Must be one of: SENDING, DELIVERED, READ, FAILED' },
          { status: 400 }
        );
      }
    }

    // Build update data
    const updateData: any = {};
    if (messageStatus !== undefined) {
      updateData.messageStatus = messageStatus.toUpperCase() as MessageStatus;
    }

    const updatedMessage = await prisma.chatMessage.update({
      where: { id: messageIdNum },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            imageUrl: true,
          },
        },
        chat: {
          select: {
            id: true,
            name: true,
            chatType: true,
          },
        },
      },
    });

    return NextResponse.json({ message: updatedMessage });
  } catch (error) {
    console.error('Error updating message:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE API to delete a message
 *
 * Query parameter:
 * - id: Message ID to delete
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const messageId = url.searchParams.get('id');

    if (!messageId) {
      return NextResponse.json({ error: 'Message ID is required' }, { status: 400 });
    }

    const messageIdNum = Number(messageId);
    if (isNaN(messageIdNum)) {
      return NextResponse.json({ error: 'Invalid message ID' }, { status: 400 });
    }

    // Check if message exists
    const existingMessage = await prisma.chatMessage.findUnique({
      where: { id: messageIdNum },
      include: {
        chat: {
          include: {
            chatUsers: {
              where: { userId: auth.userId },
            },
          },
        },
      },
    });

    if (!existingMessage) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    }

    // Check if user can delete this message (only message author, chat admin, or system admin)
    const userChatRole = existingMessage.chat.chatUsers[0];
    const canDelete =
      existingMessage.userId === auth.userId || // Message author
      userChatRole?.isAdmin || // Chat admin
      auth.isAdmin || // System admin
      auth.isOwner; // System owner

    if (!canDelete) {
      return NextResponse.json(
        { error: 'You do not have permission to delete this message' },
        { status: 403 }
      );
    }

    // Delete the message
    await prisma.chatMessage.delete({
      where: { id: messageIdNum },
    });

    return NextResponse.json({ message: 'Message deleted successfully' });
  } catch (error) {
    console.error('Error deleting message:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
