import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

/**
 * GET API for chat users
 *
 * This API supports multiple modes:
 *
 * 1. Get a single chat user by ID:
 *    - Path: /api/v1/chat-user?id=123
 *    - Returns: Detailed chat user information
 *
 * 2. Get chat users by chat ID:
 *    - Path: /api/v1/chat-user?chatId=123
 *    - Returns: List of users in the specified chat
 *
 * 3. Get chats for a specific user:
 *    - Path: /api/v1/chat-user?userId=123
 *    - Returns: List of chats where the user is a participant
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const chatUserId = url.searchParams.get('id');
    const chatId = url.searchParams.get('chatId');
    const userId = url.searchParams.get('userId');

    // Get a specific chat user by ID
    if (chatUserId) {
      const chatUserIdNum = Number(chatUserId);
      if (isNaN(chatUserIdNum)) {
        return NextResponse.json({ error: 'Invalid chat user ID' }, { status: 400 });
      }

      const chatUser = await prisma.chatUser.findUnique({
        where: { id: chatUserIdNum },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
            },
          },
          chat: {
            include: {
              organization: {
                select: { id: true, name: true },
              },
              department: {
                select: { id: true, name: true },
              },
              task: {
                select: { id: true, taskTitle: true },
              },
            },
          },
        },
      });

      if (!chatUser) {
        return NextResponse.json({ error: 'Chat user not found' }, { status: 404 });
      }

      // Check if user has access to view this chat user
      const hasAccess =
        chatUser.userId === auth.userId || // Own record
        auth.isAdmin ||
        auth.isOwner ||
        // Is participant in the same chat
        (await prisma.chatUser.findFirst({
          where: {
            chatId: chatUser.chatId,
            userId: auth.userId,
          },
        }));

      if (!hasAccess) {
        return NextResponse.json(
          { error: 'You do not have access to view this chat user' },
          { status: 403 }
        );
      }

      return NextResponse.json({ chatUser });
    }

    // Get users in a specific chat
    if (chatId) {
      const chatIdNum = Number(chatId);
      if (isNaN(chatIdNum)) {
        return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
      }

      // Check if user has access to the chat
      const userAccess = await prisma.chatUser.findFirst({
        where: {
          chatId: chatIdNum,
          userId: auth.userId,
        },
      });

      if (!userAccess && !auth.isAdmin && !auth.isOwner) {
        return NextResponse.json({ error: 'You do not have access to this chat' }, { status: 403 });
      }

      const chatUsers = await prisma.chatUser.findMany({
        where: { chatId: chatIdNum },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
            },
          },
        },
        orderBy: [
          { isAdmin: 'desc' }, // Admins first
          { joinedAt: 'asc' }, // Then by join date
        ],
      });

      return NextResponse.json({ chatUsers });
    }

    // Get chats for a specific user
    if (userId) {
      const userIdNum = Number(userId);
      if (isNaN(userIdNum)) {
        return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
      }

      // Only allow users to view their own chats unless they're admin
      if (userIdNum !== auth.userId && !auth.isAdmin && !auth.isOwner) {
        return NextResponse.json({ error: 'You can only view your own chats' }, { status: 403 });
      }

      const chatUsers = await prisma.chatUser.findMany({
        where: { userId: userIdNum },
        include: {
          chat: {
            include: {
              organization: {
                select: { id: true, name: true },
              },
              department: {
                select: { id: true, name: true },
              },
              task: {
                select: { id: true, taskTitle: true },
              },
              _count: {
                select: {
                  messages: true,
                  chatUsers: true,
                },
              },
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });

      return NextResponse.json({ chatUsers });
    }

    // If no specific filter, return error
    return NextResponse.json(
      { error: 'Please specify id, chatId, or userId parameter' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error fetching chat users:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST API to add a user to a chat
 *
 * Request body:
 * {
 *   "chatId": 123 (required),
 *   "userId": 456 (required),
 *   "isAdmin": false (optional, default: false)
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { chatId, userId, isAdmin = false } = body;

    // Validate required fields
    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
    }

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const chatIdNum = Number(chatId);
    const userIdNum = Number(userId);

    if (isNaN(chatIdNum) || isNaN(userIdNum)) {
      return NextResponse.json({ error: 'Invalid chat ID or user ID' }, { status: 400 });
    }

    // Check if chat exists
    const chat = await prisma.chat.findUnique({
      where: { id: chatIdNum },
      include: {
        chatUsers: {
          where: { userId: auth.userId },
        },
      },
    });

    if (!chat) {
      return NextResponse.json({ error: 'Chat not found' }, { status: 404 });
    }

    // Check if current user has permission to add users
    const currentUserRole = chat.chatUsers[0];
    if (!currentUserRole?.isAdmin && !auth.isAdmin && !auth.isOwner) {
      return NextResponse.json(
        { error: 'You do not have permission to add users to this chat' },
        { status: 403 }
      );
    }

    // Check if target user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: userIdNum },
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'Target user not found' }, { status: 404 });
    }

    // Check if user is already in the chat
    const existingChatUser = await prisma.chatUser.findUnique({
      where: {
        chatId_userId: {
          chatId: chatIdNum,
          userId: userIdNum,
        },
      },
    });

    if (existingChatUser) {
      return NextResponse.json(
        { error: 'User is already a participant in this chat' },
        { status: 409 }
      );
    }

    // Add user to chat
    const chatUser = await prisma.chatUser.create({
      data: {
        chatId: chatIdNum,
        userId: userIdNum,
        isAdmin,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
          },
        },
        chat: {
          select: {
            id: true,
            name: true,
            chatType: true,
          },
        },
      },
    });

    return NextResponse.json({ chatUser }, { status: 201 });
  } catch (error) {
    console.error('Error adding user to chat:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH API to update a chat user
 *
 * Request body:
 * {
 *   "id": 123 (required),
 *   "isAdmin": true/false
 * }
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id, isAdmin } = body;

    if (!id) {
      return NextResponse.json({ error: 'Chat user ID is required' }, { status: 400 });
    }

    const chatUserIdNum = Number(id);
    if (isNaN(chatUserIdNum)) {
      return NextResponse.json({ error: 'Invalid chat user ID' }, { status: 400 });
    }

    // Check if chat user exists
    const existingChatUser = await prisma.chatUser.findUnique({
      where: { id: chatUserIdNum },
      include: {
        chat: {
          include: {
            chatUsers: {
              where: { userId: auth.userId },
            },
          },
        },
      },
    });

    if (!existingChatUser) {
      return NextResponse.json({ error: 'Chat user not found' }, { status: 404 });
    }

    // Check if current user has permission to update
    const currentUserRole = existingChatUser.chat.chatUsers[0];
    if (!currentUserRole?.isAdmin && !auth.isAdmin && !auth.isOwner) {
      return NextResponse.json(
        { error: 'You do not have permission to update chat users' },
        { status: 403 }
      );
    }

    // Build update data
    const updateData: any = {};
    if (isAdmin !== undefined) updateData.isAdmin = isAdmin;

    const updatedChatUser = await prisma.chatUser.update({
      where: { id: chatUserIdNum },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
          },
        },
        chat: {
          select: {
            id: true,
            name: true,
            chatType: true,
          },
        },
      },
    });

    return NextResponse.json({ chatUser: updatedChatUser });
  } catch (error) {
    console.error('Error updating chat user:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE API to remove a user from a chat
 *
 * Query parameter:
 * - id: Chat user ID to remove
 * OR
 * - chatId & userId: Remove specific user from specific chat
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const chatUserId = url.searchParams.get('id');
    const chatId = url.searchParams.get('chatId');
    const userId = url.searchParams.get('userId');

    let targetChatUser;

    if (chatUserId) {
      const chatUserIdNum = Number(chatUserId);
      if (isNaN(chatUserIdNum)) {
        return NextResponse.json({ error: 'Invalid chat user ID' }, { status: 400 });
      }

      targetChatUser = await prisma.chatUser.findUnique({
        where: { id: chatUserIdNum },
        include: {
          chat: {
            include: {
              chatUsers: {
                where: { userId: auth.userId },
              },
            },
          },
        },
      });
    } else if (chatId && userId) {
      const chatIdNum = Number(chatId);
      const userIdNum = Number(userId);

      if (isNaN(chatIdNum) || isNaN(userIdNum)) {
        return NextResponse.json({ error: 'Invalid chat ID or user ID' }, { status: 400 });
      }

      targetChatUser = await prisma.chatUser.findUnique({
        where: {
          chatId_userId: {
            chatId: chatIdNum,
            userId: userIdNum,
          },
        },
        include: {
          chat: {
            include: {
              chatUsers: {
                where: { userId: auth.userId },
              },
            },
          },
        },
      });
    } else {
      return NextResponse.json(
        { error: 'Either chat user ID or both chat ID and user ID are required' },
        { status: 400 }
      );
    }

    if (!targetChatUser) {
      return NextResponse.json({ error: 'Chat user not found' }, { status: 404 });
    }

    // Check if current user has permission to remove users
    const currentUserRole = targetChatUser.chat.chatUsers[0];
    const isRemovingSelf = targetChatUser.userId === auth.userId;

    if (!isRemovingSelf && !currentUserRole?.isAdmin && !auth.isAdmin && !auth.isOwner) {
      return NextResponse.json(
        { error: 'You do not have permission to remove users from this chat' },
        { status: 403 }
      );
    }

    // Remove user from chat
    await prisma.chatUser.delete({
      where: { id: targetChatUser.id },
    });

    return NextResponse.json({ message: 'User removed from chat successfully' });
  } catch (error) {
    console.error('Error removing user from chat:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
