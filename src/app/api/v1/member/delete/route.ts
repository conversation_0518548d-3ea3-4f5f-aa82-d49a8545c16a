import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

// Helper function to check if user has admin rights
async function verifyAdminAccess(userId: number) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  if (!user.userRole.isAdmin && !user.userRole.isOwner) {
    return {
      error: 'You do not have permission to delete users',
      status: 403,
    };
  }

  return { user };
}

// Helper function to check if user owns the department's organization
async function verifyDepartmentAccess(departmentId: number, userId: number) {
  const department = await prisma.department.findUnique({
    where: { id: departmentId },
    include: { organization: true },
  });

  if (!department) {
    return { error: 'Department not found', status: 404 };
  }

  if (department.organization.ownerUserId !== userId) {
    return {
      error: 'You do not have permission to modify members in this department',
      status: 403,
    };
  }

  return { department };
}

// DELETE endpoint to soft delete a member
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get member ID from URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id || isNaN(Number(id))) {
      return NextResponse.json({ error: 'Valid member ID is required' }, { status: 400 });
    }

    const memberId = Number(id);

    // Get the member to delete
    const member = await prisma.departmentMember.findUnique({
      where: { id: memberId },
      include: {
        department: true,
        user: true,
      },
    });

    if (!member) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 });
    }

    // Verify department access
    const accessCheck = await verifyDepartmentAccess(member.departmentId, auth.userId);
    if ('error' in accessCheck) {
      return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
    }

    // Don't allow deleting yourself
    if (member.userId === auth.userId) {
      return NextResponse.json({ error: 'You cannot delete your own account' }, { status: 400 });
    }

    // Soft delete the user by setting deletedAt
    const deletedUser = await prisma.user.update({
      where: { id: member.userId },
      data: { deletedAt: new Date() },
    });

    return NextResponse.json({
      message: 'Member has been successfully deleted',
      userId: deletedUser.id,
      memberId: member.id,
    });
  } catch (error) {
    console.error('Error deleting member:', error);
    return NextResponse.json({ error: 'Failed to delete member' }, { status: 500 });
  }
}

// PATCH endpoint to restore a soft-deleted member
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check if the user has admin rights
    const adminCheck = await verifyAdminAccess(auth.userId);
    if ('error' in adminCheck) {
      return NextResponse.json({ error: adminCheck.error }, { status: adminCheck.status });
    }

    // Parse request body
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const userIdNum = Number(userId);
    if (isNaN(userIdNum)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if the user exists and is deleted
    const userToRestore = await prisma.user.findUnique({
      where: { id: userIdNum },
    });

    if (!userToRestore) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    if (!userToRestore.deletedAt) {
      return NextResponse.json({ error: 'User is not deleted' }, { status: 400 });
    }

    // Restore the user by setting deletedAt to null
    const restoredUser = await prisma.user.update({
      where: { id: userIdNum },
      data: { deletedAt: null },
    });

    return NextResponse.json({
      message: 'User has been successfully restored',
      userId: restoredUser.id,
    });
  } catch (error) {
    console.error('Error restoring user:', error);
    return NextResponse.json({ error: 'Failed to restore user' }, { status: 500 });
  }
}
