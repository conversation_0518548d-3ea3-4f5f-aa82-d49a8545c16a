import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

/**
 * GET API to get organizations for a specific admin user
 * 
 * Query parameters:
 * - adminUserId: Required. ID of the admin user to get organizations for
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const adminUserId = url.searchParams.get('adminUserId');

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID is required' }, { status: 400 });
    }

    const adminUserIdNum = parseInt(adminUserId);
    if (isNaN(adminUserIdNum)) {
      return NextResponse.json({ error: 'Invalid admin user ID' }, { status: 400 });
    }

    // Get all organizations where the admin user is assigned
    const adminOrganizations = await prisma.organizationAdmin.findMany({
      where: {
        userId: adminUserIdNum,
        isActive: true,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            description: true,
            imageUrl: true,
            ownerUserId: true,
          },
        },
      },
      orderBy: {
        assignedAt: 'desc',
      },
    });

    // Filter to only show organizations that the authenticated user owns
    const ownedOrganizations = adminOrganizations.filter(
      adminOrg => adminOrg.organization.ownerUserId === auth.userId
    );

    const organizations = ownedOrganizations.map(adminOrg => ({
      id: adminOrg.organization.id,
      name: adminOrg.organization.name,
      description: adminOrg.organization.description,
      imageUrl: adminOrg.organization.imageUrl,
      assignedAt: adminOrg.assignedAt,
    }));

    return NextResponse.json({ organizations });
  } catch (error) {
    console.error('Error fetching admin organizations:', error);
    return NextResponse.json({ error: 'Failed to fetch admin organizations' }, { status: 500 });
  }
}

/**
 * POST API to assign admin to organizations
 * 
 * Body parameters:
 * - adminUserId: Required. ID of the admin user
 * - organizationIds: Required. Array of organization IDs to assign
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { adminUserId, organizationIds } = body;

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID is required' }, { status: 400 });
    }

    if (!organizationIds || !Array.isArray(organizationIds) || organizationIds.length === 0) {
      return NextResponse.json({ error: 'Organization IDs array is required' }, { status: 400 });
    }

    // Verify all organizations are owned by the authenticated user
    const organizations = await prisma.organization.findMany({
      where: {
        id: { in: organizationIds },
        ownerUserId: auth.userId,
      },
    });

    if (organizations.length !== organizationIds.length) {
      return NextResponse.json({ error: 'You can only assign admins to organizations you own' }, { status: 403 });
    }

    // Get current admin assignments
    const currentAssignments = await prisma.organizationAdmin.findMany({
      where: {
        userId: adminUserId,
        organizationId: { in: organizationIds },
      },
    });

    const results = [];
    const errors = [];

    for (const orgId of organizationIds) {
      try {
        const existingAssignment = currentAssignments.find(
          assignment => assignment.organizationId === orgId
        );

        if (existingAssignment) {
          if (existingAssignment.isActive) {
            errors.push(`Admin is already assigned to organization ${orgId}`);
            continue;
          } else {
            // Reactivate existing assignment
            await prisma.organizationAdmin.update({
              where: { id: existingAssignment.id },
              data: {
                isActive: true,
                assignedAt: new Date(),
                assignedBy: auth.userId,
              },
            });
            results.push({ action: 'reactivated', organizationId: orgId });
          }
        } else {
          // Create new assignment
          await prisma.organizationAdmin.create({
            data: {
              userId: adminUserId,
              organizationId: orgId,
              assignedBy: auth.userId,
            },
          });
          results.push({ action: 'created', organizationId: orgId });
        }
      } catch (error) {
        console.error(`Error processing organization ${orgId}:`, error);
        errors.push(`Failed to assign admin to organization ${orgId}`);
      }
    }

    return NextResponse.json({
      message: `Admin assigned to ${results.length} organization(s)`,
      results,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error('Error assigning admin to organizations:', error);
    return NextResponse.json({ error: 'Failed to assign admin to organizations' }, { status: 500 });
  }
}

/**
 * DELETE API to remove admin from organizations
 * 
 * Body parameters:
 * - adminUserId: Required. ID of the admin user
 * - organizationIds: Required. Array of organization IDs to remove from
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { adminUserId, organizationIds } = body;

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID is required' }, { status: 400 });
    }

    if (!organizationIds || !Array.isArray(organizationIds) || organizationIds.length === 0) {
      return NextResponse.json({ error: 'Organization IDs array is required' }, { status: 400 });
    }

    // Verify all organizations are owned by the authenticated user
    const organizations = await prisma.organization.findMany({
      where: {
        id: { in: organizationIds },
        ownerUserId: auth.userId,
      },
    });

    if (organizations.length !== organizationIds.length) {
      return NextResponse.json({ error: 'You can only remove admins from organizations you own' }, { status: 403 });
    }

    // Remove admin from organizations
    const result = await prisma.organizationAdmin.updateMany({
      where: {
        userId: adminUserId,
        organizationId: { in: organizationIds },
        isActive: true,
      },
      data: {
        isActive: false,
      },
    });

    return NextResponse.json({
      message: `Admin removed from ${result.count} organization(s)`,
      removedCount: result.count,
    });
  } catch (error) {
    console.error('Error removing admin from organizations:', error);
    return NextResponse.json({ error: 'Failed to remove admin from organizations' }, { status: 500 });
  }
}
