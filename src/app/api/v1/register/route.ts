import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcrypt';
import { USER_ROLES } from '@/constants/roles';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const { email, password, firstName, lastName, phone, organizationName } = body;

    if (!email || !password || !firstName || !lastName || !organizationName) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json({ error: 'User with this email already exists' }, { status: 409 });
    }

    // Hash the password
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Get the Boss role ID (should be 1 based on seed file)
    const bossRole = await prisma.userRole.findFirst({
      where: { name: USER_ROLES.OWNER },
    });

    if (!bossRole) {
      return NextResponse.json({ error: 'Boss role not found' }, { status: 500 });
    }

    // Create user, organization, and default department in a transaction
    const result = await prisma.$transaction(async tx => {
      // 1. Create the user with Boss role
      const user = await tx.user.create({
        data: {
          email,
          passwordHash,
          firstName,
          lastName,
          phone,
          userRoleId: bossRole.id,
        },
      });

      // 2. Create the organization
      const organization = await tx.organization.create({
        data: {
          name: organizationName,
          ownerUserId: user.id,
        },
      });

      // 3. Create the default department
      const department = await tx.department.create({
        data: {
          name: 'default',
          organizationId: organization.id,
        },
      });

      // 4. Add the user as a member of the default department
      await tx.departmentMember.create({
        data: {
          userId: user.id,
          departmentId: department.id,
          joinedAt: new Date(),
        },
      });

      return { user, organization, department };
    });

    // Return success response with created user (excluding password)
    const { user, organization, department } = result;

    return NextResponse.json(
      {
        message: 'User registered successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            role: USER_ROLES.OWNER,
          },
          organization: {
            id: organization.id,
            name: organization.name,
          },
          department: {
            id: department.id,
            name: department.name,
          },
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json({ error: 'Failed to register user' }, { status: 500 });
  }
}
