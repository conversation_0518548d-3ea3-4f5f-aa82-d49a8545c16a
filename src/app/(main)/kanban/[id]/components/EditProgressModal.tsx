'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { X } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import useUserStore from '@/store/userStore';
import { toast } from 'react-hot-toast';
import { appTheme, appStyles } from '@/app/theme';

interface TaskProgress {
  id: number;
  taskId: number;
  updatedByUserId: number;
  progressTypeId: number;
  progressDescription: string;
  createdAt: Date;
  updatedAt: Date;
  updatedByUser: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
  progressType?: {
    id: number;
    name: string;
    description?: string;
  };
}

interface ProgressType {
  id: number;
  name: string;
  description?: string;
}

interface EditProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProgressUpdated: (updatedProgress: TaskProgress) => void;
  progress: TaskProgress;
}

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: ${appTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const ModalTitle = styled.h2`
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${appTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  transition: ${appTheme.transitions.default};

  &:hover {
    color: ${appTheme.colors.text.secondary};
    background-color: ${appTheme.colors.background.lighter};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing['2xl']};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${appTheme.spacing.base};
`;

const FormLabel = styled.label`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.secondary};
`;

const FormTextarea = styled.textarea`
  ${appStyles.input}
  min-height: 150px;
  resize: vertical;
  font-family: inherit;
`;

const FormSelect = styled.select`
  ${appStyles.input}
  background-color: ${appTheme.colors.background.light};
  font-family: inherit;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: ${appTheme.colors.error.main};
  font-size: ${appTheme.typography.fontSizes.sm};
  margin-top: ${appTheme.spacing.sm};
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
  border-top: 1px solid ${appTheme.colors.border};
`;

const CancelButton = styled.button`
  ${appStyles.button.secondary}
`;

const SubmitButton = styled.button`
  ${appStyles.button.primary}
`;

// EditProgressModal component for editing task progress
export default function EditProgressModal({
  isOpen,
  onClose,
  onProgressUpdated,
  progress,
}: EditProgressModalProps) {
  const [progressDescription, setProgressDescription] = useState('');
  const [progressTypeId, setProgressTypeId] = useState<number>(1);
  const [progressTypes, setProgressTypes] = useState<ProgressType[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { getToken } = useAuth();
  const { userData } = useUserStore();

  // Load progress types when modal opens
  useEffect(() => {
    if (isOpen) {
      setProgressDescription(progress.progressDescription || '');
      setProgressTypeId(progress.progressTypeId || 1);
      fetchProgressTypes();
    }
  }, [isOpen, progress]);

  const fetchProgressTypes = async () => {
    try {
      setIsLoading(true);
      const token = await getToken();

      const response = await fetch('/api/v1/task-progress-types', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch progress types: ${response.statusText}`);
      }

      const data = await response.json();
      setProgressTypes(data.progressTypes);
    } catch (err) {
      console.error('Error fetching progress types:', err);
      toast.error('Failed to load progress types');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!progressDescription.trim()) {
      toast.error('Please enter progress description');
      return;
    }

    // Validate that the current user is the one who created the progress update or is a boss
    if (progress.updatedByUserId !== userData?.id && !userData?.role.isOwner) {
      toast.error('You can only edit progress updates that you created');
      return;
    }

    try {
      setIsSubmitting(true);
      const token = await getToken();

      const response = await fetch('/api/v1/task-progress', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          id: progress.id,
          progressDescription,
          progressTypeId,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update progress: ${response.statusText}`);
      }

      const data = await response.json();
      onProgressUpdated(data.taskProgress);

      toast.success('Progress updated successfully');
      onClose();
    } catch (err) {
      console.error('Error updating progress:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to update progress');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>Edit Task Progress</ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>
        <ModalBody>
          <form onSubmit={handleSubmit}>
            <FormGroup>
              <FormLabel htmlFor="progressTypeId">Progress Type</FormLabel>
              <FormSelect
                id="progressTypeId"
                value={progressTypeId}
                onChange={e => setProgressTypeId(Number(e.target.value))}
                disabled={true}
              >
                {progressTypes.map(type => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </FormSelect>
            </FormGroup>
            <FormGroup>
              <FormLabel htmlFor="progressDescription">Progress Description</FormLabel>
              <FormTextarea
                id="progressDescription"
                value={progressDescription}
                onChange={e => setProgressDescription(e.target.value)}
                placeholder="Enter progress details"
                disabled={isSubmitting}
              />
            </FormGroup>
          </form>
        </ModalBody>

        <ModalFooter>
          <CancelButton type="button" onClick={onClose}>
            Cancel
          </CancelButton>
          <SubmitButton type="button" onClick={handleSubmit} disabled={isSubmitting || isLoading}>
            {isSubmitting ? 'Updating...' : 'Update Progress'}
          </SubmitButton>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
}
