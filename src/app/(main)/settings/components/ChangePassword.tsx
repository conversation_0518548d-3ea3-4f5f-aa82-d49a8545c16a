'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { useRouter } from 'next/navigation';
import { getCookie } from 'cookies-next';
import { appTheme as settingsTheme, appStyles as commonStyles } from '@/app/theme';

const PasswordContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: ${settingsTheme.spacing.xl};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  box-shadow: ${settingsTheme.shadows.md};

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.base};
    gap: ${settingsTheme.spacing.base};
  }
`;

const SectionTitle = styled.h2`
  ${commonStyles.sectionTitle}
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
  width: 100%;
`;

const Label = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.secondary};
  margin-bottom: ${settingsTheme.spacing.xs};
`;

const Input = styled.input`
  ${commonStyles.input}
`;

const Button = styled.button`
  ${commonStyles.button.primary}
`;

const ErrorMessage = styled.div`
  color: ${settingsTheme.colors.error.main};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.error.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  border-left: 3px solid ${settingsTheme.colors.error.main};
`;

const SuccessMessage = styled.div`
  color: ${settingsTheme.colors.success.main};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.success.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  border-left: 3px solid ${settingsTheme.colors.success.main};
`;

export default function ChangePassword() {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate password inputs
    if (!currentPassword) {
      setError('Current password is required');
      return;
    }

    if (!newPassword) {
      setError('New password is required');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('New passwords do not match');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      const token = getCookie('access_token');

      if (!token) {
        setError('Authentication required. Please log in.');
        router.push('/login');
        return;
      }

      // Call the dedicated change password API
      const response = await fetch('/api/v1/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          currentPassword,
          newPassword,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to change password');
      }

      // Show success message
      setSuccess('Password updated successfully');

      // Reset password fields
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (err: any) {
      console.error('Error changing password:', err);
      setError(err.message || 'Failed to change password. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PasswordContainer>
      <SectionTitle>Change Password</SectionTitle>

      <form onSubmit={handlePasswordChange}>
        {error && <ErrorMessage>{error}</ErrorMessage>}
        {success && <SuccessMessage>{success}</SuccessMessage>}

        <FormGroup>
          <Label htmlFor="currentPassword">Current Password</Label>
          <Input
            id="currentPassword"
            type="password"
            value={currentPassword}
            onChange={e => setCurrentPassword(e.target.value)}
            placeholder="Enter your current password"
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="newPassword">New Password</Label>
          <Input
            id="newPassword"
            type="password"
            value={newPassword}
            onChange={e => setNewPassword(e.target.value)}
            placeholder="Enter your new password"
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="confirmPassword">Confirm New Password</Label>
          <Input
            id="confirmPassword"
            type="password"
            value={confirmPassword}
            onChange={e => setConfirmPassword(e.target.value)}
            placeholder="Confirm your new password"
          />
        </FormGroup>

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Updating...' : 'Update Password'}
        </Button>
      </form>
    </PasswordContainer>
  );
}
