'use client';

import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { appTheme as settingsTheme } from '@/app/theme';

const SettingsContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: ${settingsTheme.spacing.xl};
  gap: ${settingsTheme.spacing.xl};
  max-width: 1400px;
  margin: 0 auto;
  // background-color: #ebedee;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: ${settingsTheme.borderRadius.lg};

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: 0;
    gap: ${settingsTheme.spacing.xl};
  }
`;

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${settingsTheme.spacing.lg};
  border-radius: ${settingsTheme.borderRadius.lg};
  background-color: ${settingsTheme.colors.background.main};
  box-shadow: ${settingsTheme.shadows.sm};
`;

const SettingsTitle = styled.h1`
  font-size: ${settingsTheme.typography.fontSizes['3xl']};
  font-weight: ${settingsTheme.typography.fontWeights.bold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.md};

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes['2xl']};
  }
`;

const SettingsContent = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
`;

const ContentArea = styled.div`
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: 0;
  background-color: transparent;
  box-shadow: none;
  overflow: hidden;
`;

interface SettingsLayoutProps {
  children: ReactNode;
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  return (
    <SettingsContainer>
      <SettingsHeader>
        <SettingsTitle>Settings</SettingsTitle>
      </SettingsHeader>
      <SettingsContent>
        <ContentArea>{children}</ContentArea>
      </SettingsContent>
    </SettingsContainer>
  );
}
