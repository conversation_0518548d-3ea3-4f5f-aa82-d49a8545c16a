'use client';

import React from 'react';
import styled from 'styled-components';
import { appTheme as settingsTheme } from '@/app/theme';

const TabContainer = styled.div`
  display: flex;
  border-bottom: 1px solid ${settingsTheme.colors.border};
  margin-bottom: ${settingsTheme.spacing.base};
  width: 100%;
  overflow-x: auto;
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding-bottom: ${settingsTheme.spacing.xs};
  }
`;

interface TabItemProps {
  $active: boolean;
}

const TabItem = styled.button<TabItemProps>`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.sm};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${props =>
    props.$active
      ? settingsTheme.typography.fontWeights.bold
      : settingsTheme.typography.fontWeights.medium};
  color: ${props =>
    props.$active ? settingsTheme.colors.primary : settingsTheme.colors.text.secondary};
  background-color: transparent;
  border: none;
  border-bottom: 3px solid
    ${props => (props.$active ? settingsTheme.colors.primary : 'transparent')};
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    color: ${settingsTheme.colors.primary};
  }

  &:focus {
    outline: none;
  }
`;

export interface Tab {
  id: string;
  label: string;
}

interface TabNavigationProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

export default function TabNavigation({ tabs, activeTab, onTabChange }: TabNavigationProps) {
  return (
    <TabContainer>
      {tabs.map(tab => (
        <TabItem key={tab.id} $active={activeTab === tab.id} onClick={() => onTabChange(tab.id)}>
          {tab.label}
        </TabItem>
      ))}
    </TabContainer>
  );
}
