const jwt = require('jsonwebtoken');

// Create a test JWT token for owner (userRoleId = 1)
const token = jwt.sign(
  { 
    userId: 1, 
    email: '<EMAIL>',
    userRoleId: 1
  }, 
  'secret', // JWT_SECRET from .env
  { expiresIn: '24h' }
);

console.log('Generated JWT Token:');
console.log(token);

// Test create organization API
async function testCreateOrganization() {
  try {
    const response = await fetch('http://localhost:3000/api/v1/organization', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        name: 'Test Organization',
        description: 'This is a test organization created via API'
      })
    });

    const data = await response.json();
    
    console.log('\n=== CREATE ORGANIZATION TEST ===');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Organization created successfully!');
    } else {
      console.log('❌ Failed to create organization');
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

// Test get organizations API
async function testGetOrganizations() {
  try {
    const response = await fetch('http://localhost:3000/api/v1/organization', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const data = await response.json();
    
    console.log('\n=== GET ORGANIZATIONS TEST ===');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Organizations fetched successfully!');
      console.log(`Found ${data.organizations?.length || 0} organizations`);
    } else {
      console.log('❌ Failed to fetch organizations');
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting API tests...\n');
  
  await testGetOrganizations();
  await testCreateOrganization();
  await testGetOrganizations(); // Check if new org was created
  
  console.log('\n✨ Tests completed!');
}

runTests();
